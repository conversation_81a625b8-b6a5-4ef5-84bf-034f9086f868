from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from functools import wraps
from models import User, ChatSession, EmergencyAlert, LearningModule, GameProgress, db
from datetime import datetime, timedelta
import json

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('Admin access required.', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/')
@login_required
@admin_required
def admin_dashboard():
    """Admin dashboard with overview statistics"""
    # User statistics
    total_users = User.query.count()
    new_users_today = User.query.filter(
        User.created_at >= datetime.utcnow().date()
    ).count()
    active_users_week = User.query.filter(
        User.last_login >= datetime.utcnow() - timedelta(days=7)
    ).count()
    
    # Chat statistics
    total_sessions = ChatSession.query.count()
    crisis_sessions = ChatSession.query.filter_by(crisis_detected=True).count()
    sessions_today = ChatSession.query.filter(
        ChatSession.session_start >= datetime.utcnow().date()
    ).count()
    
    # Emergency statistics
    total_alerts = EmergencyAlert.query.count()
    alerts_today = EmergencyAlert.query.filter(
        EmergencyAlert.timestamp >= datetime.utcnow().date()
    ).count()
    unresolved_alerts = EmergencyAlert.query.filter_by(resolved=False).count()
    
    # Learning statistics
    total_modules = LearningModule.query.count()
    active_modules = LearningModule.query.filter_by(is_active=True).count()
    
    # Game statistics
    total_game_sessions = GameProgress.query.count()
    active_players = GameProgress.query.filter(
        GameProgress.last_played >= datetime.utcnow() - timedelta(days=7)
    ).count()
    
    stats = {
        'users': {
            'total': total_users,
            'new_today': new_users_today,
            'active_week': active_users_week
        },
        'chat': {
            'total_sessions': total_sessions,
            'crisis_sessions': crisis_sessions,
            'sessions_today': sessions_today
        },
        'emergency': {
            'total_alerts': total_alerts,
            'alerts_today': alerts_today,
            'unresolved': unresolved_alerts
        },
        'learning': {
            'total_modules': total_modules,
            'active_modules': active_modules
        },
        'games': {
            'total_sessions': total_game_sessions,
            'active_players': active_players
        }
    }
    
    return render_template('admin/dashboard.html', stats=stats)

@admin_bp.route('/users')
@login_required
@admin_required
def manage_users():
    """Manage users"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    users_query = User.query
    
    if search:
        users_query = users_query.filter(
            User.username.contains(search) | 
            User.email.contains(search) |
            User.first_name.contains(search) |
            User.last_name.contains(search)
        )
    
    users = users_query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/users.html', users=users, search=search)

@admin_bp.route('/users/<int:user_id>')
@login_required
@admin_required
def view_user(user_id):
    """View detailed user information"""
    user = User.query.get_or_404(user_id)
    
    # Get user's chat sessions
    chat_sessions = ChatSession.query.filter_by(user_id=user_id)\
                                   .order_by(ChatSession.session_start.desc())\
                                   .limit(10).all()
    
    # Get user's emergency alerts
    emergency_alerts = EmergencyAlert.query.filter_by(user_id=user_id)\
                                          .order_by(EmergencyAlert.timestamp.desc())\
                                          .limit(10).all()
    
    # Get user's game progress
    game_progress = GameProgress.query.filter_by(user_id=user_id).all()
    
    return render_template('admin/user_detail.html', 
                         user=user,
                         chat_sessions=chat_sessions,
                         emergency_alerts=emergency_alerts,
                         game_progress=game_progress)

@admin_bp.route('/learning_modules')
@login_required
@admin_required
def manage_learning_modules():
    """Manage learning modules"""
    modules = LearningModule.query.order_by(LearningModule.created_at.desc()).all()
    return render_template('admin/learning_modules.html', modules=modules)

@admin_bp.route('/learning_modules/new', methods=['GET', 'POST'])
@login_required
@admin_required
def create_learning_module():
    """Create new learning module"""
    if request.method == 'POST':
        title = request.form['title']
        description = request.form['description']
        content = request.form['content']
        module_type = request.form['module_type']
        difficulty_level = request.form['difficulty_level']
        estimated_time = request.form.get('estimated_time', type=int)
        language = request.form.get('language', 'en')
        
        module = LearningModule(
            title=title,
            description=description,
            content=content,
            module_type=module_type,
            difficulty_level=difficulty_level,
            estimated_time=estimated_time,
            language=language
        )
        
        db.session.add(module)
        db.session.commit()
        
        flash('Learning module created successfully!', 'success')
        return redirect(url_for('admin.manage_learning_modules'))
    
    return render_template('admin/create_module.html')

@admin_bp.route('/learning_modules/<int:module_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_learning_module(module_id):
    """Edit learning module"""
    module = LearningModule.query.get_or_404(module_id)
    
    if request.method == 'POST':
        module.title = request.form['title']
        module.description = request.form['description']
        module.content = request.form['content']
        module.module_type = request.form['module_type']
        module.difficulty_level = request.form['difficulty_level']
        module.estimated_time = request.form.get('estimated_time', type=int)
        module.language = request.form.get('language', 'en')
        module.is_active = bool(request.form.get('is_active'))
        
        db.session.commit()
        
        flash('Learning module updated successfully!', 'success')
        return redirect(url_for('admin.manage_learning_modules'))
    
    return render_template('admin/edit_module.html', module=module)

@admin_bp.route('/learning_modules/<int:module_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_learning_module(module_id):
    """Delete learning module"""
    module = LearningModule.query.get_or_404(module_id)
    db.session.delete(module)
    db.session.commit()
    
    flash('Learning module deleted successfully!', 'success')
    return redirect(url_for('admin.manage_learning_modules'))

@admin_bp.route('/emergency_alerts')
@login_required
@admin_required
def manage_emergency_alerts():
    """Manage emergency alerts"""
    page = request.args.get('page', 1, type=int)
    alert_type = request.args.get('type')
    resolved = request.args.get('resolved')
    
    alerts_query = EmergencyAlert.query
    
    if alert_type:
        alerts_query = alerts_query.filter_by(alert_type=alert_type)
    
    if resolved == 'true':
        alerts_query = alerts_query.filter_by(resolved=True)
    elif resolved == 'false':
        alerts_query = alerts_query.filter_by(resolved=False)
    
    alerts = alerts_query.order_by(EmergencyAlert.timestamp.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/emergency_alerts.html', alerts=alerts)

@admin_bp.route('/emergency_alerts/<int:alert_id>/resolve', methods=['POST'])
@login_required
@admin_required
def resolve_alert(alert_id):
    """Mark emergency alert as resolved"""
    alert = EmergencyAlert.query.get_or_404(alert_id)
    alert.resolved = True
    db.session.commit()
    
    return jsonify({'status': 'success'})

@admin_bp.route('/chat_sessions')
@login_required
@admin_required
def manage_chat_sessions():
    """Manage chat sessions"""
    page = request.args.get('page', 1, type=int)
    crisis_only = request.args.get('crisis') == 'true'
    
    sessions_query = ChatSession.query
    
    if crisis_only:
        sessions_query = sessions_query.filter_by(crisis_detected=True)
    
    sessions = sessions_query.order_by(ChatSession.session_start.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/chat_sessions.html', sessions=sessions, crisis_only=crisis_only)

@admin_bp.route('/analytics')
@login_required
@admin_required
def analytics():
    """Analytics dashboard"""
    # User growth over time
    user_growth = []
    for i in range(30, 0, -1):
        date = datetime.utcnow().date() - timedelta(days=i)
        count = User.query.filter(User.created_at <= date).count()
        user_growth.append({'date': date.isoformat(), 'count': count})
    
    # Chat sessions over time
    chat_activity = []
    for i in range(7, 0, -1):
        date = datetime.utcnow().date() - timedelta(days=i)
        count = ChatSession.query.filter(
            ChatSession.session_start >= date,
            ChatSession.session_start < date + timedelta(days=1)
        ).count()
        chat_activity.append({'date': date.isoformat(), 'count': count})
    
    # Emergency alerts by type
    alert_types = db.session.query(
        EmergencyAlert.alert_type,
        db.func.count(EmergencyAlert.id).label('count')
    ).group_by(EmergencyAlert.alert_type).all()
    
    # Game popularity
    game_popularity = db.session.query(
        GameProgress.game_name,
        db.func.count(GameProgress.id).label('players')
    ).group_by(GameProgress.game_name).all()
    
    analytics_data = {
        'user_growth': user_growth,
        'chat_activity': chat_activity,
        'alert_types': [{'type': t[0], 'count': t[1]} for t in alert_types],
        'game_popularity': [{'game': g[0], 'players': g[1]} for g in game_popularity]
    }
    
    return render_template('admin/analytics.html', analytics=analytics_data)

@admin_bp.route('/system_settings', methods=['GET', 'POST'])
@login_required
@admin_required
def system_settings():
    """System settings"""
    if request.method == 'POST':
        # Handle system settings updates
        # This would typically involve updating configuration files or database settings
        flash('System settings updated successfully!', 'success')
        return redirect(url_for('admin.system_settings'))
    
    return render_template('admin/settings.html')
