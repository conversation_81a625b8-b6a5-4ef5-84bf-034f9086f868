{% extends "base.html" %}

{% block title %}Welcome - Mental Wellness App{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="row align-items-center min-vh-100">
        <div class="col-lg-6">
            <div class="hero-content">
                <h1 class="display-4 fw-bold text-primary mb-4">
                    Your Mental Wellness Journey Starts Here
                </h1>
                <p class="lead mb-4">
                    Get 24/7 AI-powered mental health support, emergency assistance, 
                    interactive wellness games, and personalized learning modules 
                    to support your mental wellbeing.
                </p>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="fas fa-user-plus"></i> Get Started Free
                    </a>
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg px-4">
                        <i class="fas fa-sign-in-alt"></i> Sign In
                    </a>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt"></i> 
                        Your privacy and safety are our top priorities
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="hero-image text-center">
                <div class="feature-preview">
                    <div class="card shadow-lg">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-robot text-primary"></i> 
                                AI Mental Health Counselor
                            </h5>
                            <p class="card-text">
                                "I'm here to listen and support you 24/7. 
                                How are you feeling today?"
                            </p>
                            <div class="chat-preview">
                                <div class="message user-message">
                                    I'm feeling anxious about work...
                                </div>
                                <div class="message bot-message">
                                    I understand that work anxiety can be overwhelming. 
                                    Let's explore some coping strategies together...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row py-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Comprehensive Mental Wellness Tools</h2>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 text-center feature-card">
                <div class="card-body">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-comments fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">AI Counselor</h5>
                    <p class="card-text">
                        24/7 AI-powered mental health support with crisis detection 
                        and professional referrals when needed.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 text-center feature-card">
                <div class="card-body">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">Emergency Support</h5>
                    <p class="card-text">
                        Instant emergency alerts, location sharing, and direct 
                        connection to crisis hotlines and emergency contacts.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 text-center feature-card">
                <div class="card-body">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-gamepad fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">Wellness Games</h5>
                    <p class="card-text">
                        Interactive games for breathing exercises, meditation, 
                        mood tracking, and stress relief activities.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-4">
            <div class="card h-100 text-center feature-card">
                <div class="card-body">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-book fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">Learning Modules</h5>
                    <p class="card-text">
                        Educational content about mental health awareness, 
                        coping strategies, and wellness techniques.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Safety & Privacy Section -->
    <div class="row py-5 bg-light rounded">
        <div class="col-12">
            <h2 class="text-center mb-4">Your Safety & Privacy Matter</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-lock fa-2x text-primary mb-2"></i>
                    <h5>Secure & Private</h5>
                    <p>End-to-end encryption and anonymous data handling</p>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                    <h5>Crisis Detection</h5>
                    <p>AI monitors for crisis situations and provides immediate resources</p>
                </div>
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                    <h5>Professional Support</h5>
                    <p>Direct connections to licensed mental health professionals</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Resources -->
    <div class="row py-5">
        <div class="col-12">
            <div class="alert alert-info">
                <h4 class="alert-heading">
                    <i class="fas fa-info-circle"></i> 
                    Immediate Help Available
                </h4>
                <p class="mb-0">
                    If you're in crisis or having thoughts of self-harm, please reach out immediately:
                </p>
                <hr>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Emergency:</strong> 911
                    </div>
                    <div class="col-md-4">
                        <strong>Crisis Text Line:</strong> Text HOME to 741741
                    </div>
                    <div class="col-md-4">
                        <strong>Suicide Prevention:</strong> 988
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.hero-content {
    padding: 2rem 0;
}

.feature-card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.feature-icon {
    opacity: 0.8;
}

.chat-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.message {
    margin: 0.5rem 0;
    padding: 0.5rem;
    border-radius: 10px;
    font-size: 0.9rem;
}

.user-message {
    background: #007bff;
    color: white;
    text-align: right;
}

.bot-message {
    background: #e9ecef;
    color: #333;
    text-align: left;
}

.feature-preview {
    max-width: 400px;
    margin: 0 auto;
}
</style>
{% endblock %}
