from flask import Blueprint, render_template, request, jsonify, flash
from flask_login import login_required, current_user
from models import GameProgress, User, db
from datetime import datetime
import json

games_bp = Blueprint('games', __name__)

# Available wellness games
AVAILABLE_GAMES = {
    'breathing': {
        'name': 'Breathing Exercise',
        'description': 'Guided breathing exercises to reduce stress and anxiety',
        'max_level': 10,
        'points_per_level': 5
    },
    'meditation': {
        'name': 'Mindfulness Meditation',
        'description': 'Progressive meditation sessions for mental clarity',
        'max_level': 15,
        'points_per_level': 8
    },
    'mood_tracker': {
        'name': 'Daily Mood Tracker',
        'description': 'Track and understand your emotional patterns',
        'max_level': 30,
        'points_per_level': 3
    },
    'gratitude': {
        'name': 'Gratitude Journal',
        'description': 'Daily gratitude practice to improve mental wellbeing',
        'max_level': 20,
        'points_per_level': 4
    },
    'stress_relief': {
        'name': 'Stress Relief Activities',
        'description': 'Interactive activities to manage and reduce stress',
        'max_level': 12,
        'points_per_level': 6
    }
}

def get_or_create_game_progress(user_id, game_name):
    """Get existing game progress or create new one"""
    progress = GameProgress.query.filter_by(user_id=user_id, game_name=game_name).first()
    if not progress:
        progress = GameProgress(user_id=user_id, game_name=game_name)
        db.session.add(progress)
        db.session.commit()
    return progress

def award_points(user, points):
    """Award points to user and check for level up"""
    user.points += points
    
    # Simple level calculation: every 100 points = 1 level
    new_level = (user.points // 100) + 1
    level_up = new_level > user.level
    user.level = new_level
    
    db.session.commit()
    return level_up

@games_bp.route('/')
@login_required
def games_home():
    """Games dashboard"""
    user_progress = {}
    for game_name in AVAILABLE_GAMES.keys():
        progress = GameProgress.query.filter_by(user_id=current_user.id, game_name=game_name).first()
        user_progress[game_name] = progress
    
    return render_template('games/dashboard.html', 
                         games=AVAILABLE_GAMES, 
                         user_progress=user_progress)

@games_bp.route('/breathing')
@login_required
def breathing_game():
    """Breathing exercise game"""
    progress = get_or_create_game_progress(current_user.id, 'breathing')
    return render_template('games/breathing.html', progress=progress, game_info=AVAILABLE_GAMES['breathing'])

@games_bp.route('/breathing/complete', methods=['POST'])
@login_required
def complete_breathing():
    """Complete breathing exercise session"""
    data = request.json
    duration = data.get('duration', 0)  # in seconds
    cycles_completed = data.get('cycles', 0)
    
    progress = get_or_create_game_progress(current_user.id, 'breathing')
    
    # Award points based on duration and cycles
    base_points = AVAILABLE_GAMES['breathing']['points_per_level']
    bonus_points = min(duration // 60, 5)  # Bonus for longer sessions (max 5)
    total_points = base_points + bonus_points
    
    # Update progress
    progress.score += total_points
    progress.last_played = datetime.utcnow()
    
    # Check for level up
    if cycles_completed >= progress.level * 5:  # 5 cycles per level
        if progress.level < AVAILABLE_GAMES['breathing']['max_level']:
            progress.level += 1
    
    level_up = award_points(current_user, total_points)
    
    return jsonify({
        'points_earned': total_points,
        'total_points': current_user.points,
        'level_up': level_up,
        'new_level': current_user.level,
        'game_level': progress.level
    })

@games_bp.route('/meditation')
@login_required
def meditation_game():
    """Meditation game"""
    progress = get_or_create_game_progress(current_user.id, 'meditation')
    return render_template('games/meditation.html', progress=progress, game_info=AVAILABLE_GAMES['meditation'])

@games_bp.route('/meditation/complete', methods=['POST'])
@login_required
def complete_meditation():
    """Complete meditation session"""
    data = request.json
    duration = data.get('duration', 0)
    focus_score = data.get('focus_score', 0)  # 0-100
    
    progress = get_or_create_game_progress(current_user.id, 'meditation')
    
    # Award points based on duration and focus
    base_points = AVAILABLE_GAMES['meditation']['points_per_level']
    duration_bonus = min(duration // 120, 8)  # Bonus for longer sessions
    focus_bonus = focus_score // 20  # Bonus for better focus
    total_points = base_points + duration_bonus + focus_bonus
    
    progress.score += total_points
    progress.last_played = datetime.utcnow()
    
    # Level up based on consistent practice
    if duration >= 300 and focus_score >= 60:  # 5 min + good focus
        if progress.level < AVAILABLE_GAMES['meditation']['max_level']:
            progress.level += 1
    
    level_up = award_points(current_user, total_points)
    
    return jsonify({
        'points_earned': total_points,
        'total_points': current_user.points,
        'level_up': level_up,
        'new_level': current_user.level,
        'game_level': progress.level
    })

@games_bp.route('/mood_tracker')
@login_required
def mood_tracker():
    """Daily mood tracker"""
    progress = get_or_create_game_progress(current_user.id, 'mood_tracker')
    return render_template('games/mood_tracker.html', progress=progress, game_info=AVAILABLE_GAMES['mood_tracker'])

@games_bp.route('/mood_tracker/submit', methods=['POST'])
@login_required
def submit_mood():
    """Submit daily mood entry"""
    data = request.json
    mood_rating = data.get('mood_rating', 5)  # 1-10 scale
    mood_notes = data.get('notes', '')
    activities = data.get('activities', [])
    
    progress = get_or_create_game_progress(current_user.id, 'mood_tracker')
    
    # Award points for daily tracking
    base_points = AVAILABLE_GAMES['mood_tracker']['points_per_level']
    consistency_bonus = 2 if progress.last_played and \
                           (datetime.utcnow() - progress.last_played).days == 1 else 0
    
    total_points = base_points + consistency_bonus
    
    progress.score += total_points
    progress.last_played = datetime.utcnow()
    
    # Level up based on consecutive days
    consecutive_days = progress.level  # Simplified tracking
    if consecutive_days < AVAILABLE_GAMES['mood_tracker']['max_level']:
        progress.level += 1
    
    level_up = award_points(current_user, total_points)
    
    return jsonify({
        'points_earned': total_points,
        'total_points': current_user.points,
        'level_up': level_up,
        'new_level': current_user.level,
        'streak_days': progress.level
    })

@games_bp.route('/gratitude')
@login_required
def gratitude_journal():
    """Gratitude journal game"""
    progress = get_or_create_game_progress(current_user.id, 'gratitude')
    return render_template('games/gratitude.html', progress=progress, game_info=AVAILABLE_GAMES['gratitude'])

@games_bp.route('/gratitude/submit', methods=['POST'])
@login_required
def submit_gratitude():
    """Submit gratitude entry"""
    data = request.json
    gratitude_items = data.get('items', [])
    reflection = data.get('reflection', '')
    
    progress = get_or_create_game_progress(current_user.id, 'gratitude')
    
    # Award points based on number of items and reflection
    base_points = AVAILABLE_GAMES['gratitude']['points_per_level']
    item_bonus = len(gratitude_items)
    reflection_bonus = 2 if len(reflection) > 50 else 0
    
    total_points = base_points + item_bonus + reflection_bonus
    
    progress.score += total_points
    progress.last_played = datetime.utcnow()
    
    # Level up based on entries
    if len(gratitude_items) >= 3:  # Quality entry
        if progress.level < AVAILABLE_GAMES['gratitude']['max_level']:
            progress.level += 1
    
    level_up = award_points(current_user, total_points)
    
    return jsonify({
        'points_earned': total_points,
        'total_points': current_user.points,
        'level_up': level_up,
        'new_level': current_user.level,
        'game_level': progress.level
    })

@games_bp.route('/stress_relief')
@login_required
def stress_relief():
    """Stress relief activities"""
    progress = get_or_create_game_progress(current_user.id, 'stress_relief')
    return render_template('games/stress_relief.html', progress=progress, game_info=AVAILABLE_GAMES['stress_relief'])

@games_bp.route('/leaderboard')
@login_required
def leaderboard():
    """Global leaderboard"""
    top_users = User.query.order_by(User.points.desc()).limit(10).all()
    user_rank = User.query.filter(User.points > current_user.points).count() + 1
    
    return render_template('games/leaderboard.html', 
                         top_users=top_users, 
                         user_rank=user_rank)

@games_bp.route('/achievements')
@login_required
def achievements():
    """User achievements and badges"""
    # Get all game progress for user
    all_progress = GameProgress.query.filter_by(user_id=current_user.id).all()
    
    # Calculate achievements
    achievements = []
    
    # Point-based achievements
    if current_user.points >= 100:
        achievements.append({'name': 'First Century', 'description': 'Earned 100 points', 'icon': '🏆'})
    if current_user.points >= 500:
        achievements.append({'name': 'Wellness Warrior', 'description': 'Earned 500 points', 'icon': '⚔️'})
    if current_user.points >= 1000:
        achievements.append({'name': 'Mental Health Champion', 'description': 'Earned 1000 points', 'icon': '👑'})
    
    # Game-specific achievements
    for progress in all_progress:
        if progress.level >= 5:
            game_name = AVAILABLE_GAMES.get(progress.game_name, {}).get('name', progress.game_name)
            achievements.append({
                'name': f'{game_name} Expert',
                'description': f'Reached level 5 in {game_name}',
                'icon': '🌟'
            })
    
    return render_template('games/achievements.html', achievements=achievements)
