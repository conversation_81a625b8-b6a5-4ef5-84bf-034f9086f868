{% extends "base.html" %}

{% block title %}Dashboard - Mental Wellness App{% endblock %}

{% block content %}
<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card bg-gradient-primary text-white rounded p-4">
                <h1 class="h3 mb-2">
                    Welcome back, {{ current_user.first_name or current_user.username }}! 
                    <i class="fas fa-heart text-warning"></i>
                </h1>
                <p class="mb-0">
                    How are you feeling today? Your mental wellness journey continues here.
                </p>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ current_user.points }}</h4>
                            <p class="mb-0">Wellness Points</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>Level {{ current_user.level }}</h4>
                            <p class="mb-0">Wellness Level</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-trophy fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="streakDays">0</h4>
                            <p class="mb-0">Day Streak</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-fire fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="todayMood">😊</h4>
                            <p class="mb-0">Today's Mood</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-smile fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">Quick Actions</h3>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-3">
            <a href="{{ url_for('chat.chat_home') }}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Start Chat Session</h5>
                    <p class="card-text">Talk to your AI counselor</p>
                </div>
            </a>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-3">
            <a href="{{ url_for('games.mood_tracker') }}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                    <h5 class="card-title">Track Mood</h5>
                    <p class="card-text">Log your daily mood</p>
                </div>
            </a>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-3">
            <a href="{{ url_for('games.breathing_game') }}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-wind fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Breathing Exercise</h5>
                    <p class="card-text">Calm your mind</p>
                </div>
            </a>
        </div>
        
        <div class="col-md-6 col-lg-3 mb-3">
            <a href="{{ url_for('learning.learning_home') }}" class="card quick-action-card text-decoration-none">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Learn Something</h5>
                    <p class="card-text">Explore wellness topics</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Recent Activity & Progress -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> 
                        Your Progress This Week
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="progressChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> 
                        Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="fas fa-comments text-white"></i>
                        </div>
                        <div class="activity-content">
                            <p class="mb-1">Completed chat session</p>
                            <small class="text-muted">2 hours ago</small>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="fas fa-gamepad text-white"></i>
                        </div>
                        <div class="activity-content">
                            <p class="mb-1">Breathing exercise completed</p>
                            <small class="text-muted">Yesterday</small>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-info">
                            <i class="fas fa-book text-white"></i>
                        </div>
                        <div class="activity-content">
                            <p class="mb-1">Finished learning module</p>
                            <small class="text-muted">2 days ago</small>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary btn-sm">View All Activity</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Check-in -->
    <div class="row">
        <div class="col-12">
            <div class="card daily-checkin">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check"></i> 
                        Daily Check-in
                    </h5>
                </div>
                <div class="card-body">
                    <p>Take a moment to reflect on your day and set intentions for tomorrow.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">How are you feeling right now?</label>
                                <div class="mood-selector">
                                    <button class="btn btn-outline-primary mood-btn" data-mood="😢">😢</button>
                                    <button class="btn btn-outline-primary mood-btn" data-mood="😐">😐</button>
                                    <button class="btn btn-outline-primary mood-btn" data-mood="😊">😊</button>
                                    <button class="btn btn-outline-primary mood-btn" data-mood="😄">😄</button>
                                    <button class="btn btn-outline-primary mood-btn" data-mood="🤩">🤩</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">What are you grateful for today?</label>
                                <textarea class="form-control" rows="3" placeholder="Write something you're grateful for..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <button class="btn btn-success" onclick="submitDailyCheckin()">
                            <i class="fas fa-check"></i> Complete Check-in
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.quick-action-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    text-decoration: none;
}

.activity-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.activity-content {
    flex: 1;
}

.mood-selector {
    display: flex;
    gap: 0.5rem;
}

.mood-btn {
    font-size: 1.5rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.mood-btn.active {
    background-color: #007bff;
    border-color: #007bff;
}

.daily-checkin {
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize progress chart
const ctx = document.getElementById('progressChart').getContext('2d');
const progressChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Wellness Points',
            data: [12, 19, 15, 25, 22, 30, 28],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Mood selector functionality
document.querySelectorAll('.mood-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.mood-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('todayMood').textContent = this.dataset.mood;
    });
});

// Daily check-in submission
function submitDailyCheckin() {
    const selectedMood = document.querySelector('.mood-btn.active');
    const gratitude = document.querySelector('textarea').value;
    
    if (!selectedMood) {
        alert('Please select your mood first!');
        return;
    }
    
    // Here you would send the data to the server
    console.log('Mood:', selectedMood.dataset.mood);
    console.log('Gratitude:', gratitude);
    
    alert('Daily check-in completed! +5 wellness points earned.');
    
    // Reset form
    document.querySelectorAll('.mood-btn').forEach(b => b.classList.remove('active'));
    document.querySelector('textarea').value = '';
}
</script>
{% endblock %}
