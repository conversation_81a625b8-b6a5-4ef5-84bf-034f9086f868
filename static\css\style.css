/* Mental Wellness App - Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container-fluid {
    max-width: 1400px;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Emergency Button */
.emergency-button-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.emergency-btn {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4);
    animation: pulse-emergency 2s infinite;
    transition: var(--transition);
}

.emergency-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(220, 53, 69, 0.6);
}

.emergency-btn.pressed {
    animation: emergency-countdown 3s linear;
    background: linear-gradient(45deg, #dc3545, #c82333);
}

@keyframes pulse-emergency {
    0% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4); }
    50% { box-shadow: 0 4px 30px rgba(220, 53, 69, 0.7); }
    100% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.4); }
}

@keyframes emergency-countdown {
    0% { background: #dc3545; }
    100% { background: #721c24; }
}

/* Chat Interface */
.chat-container {
    height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    background: white;
}

.chat-message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
}

.chat-message.user {
    justify-content: flex-end;
}

.chat-message.bot {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message-bubble.user {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message-bubble.bot {
    background: #e9ecef;
    color: #333;
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Mood Tracker */
.mood-scale {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.mood-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 2px solid transparent;
}

.mood-option:hover {
    background: #f8f9fa;
    border-color: var(--primary-color);
}

.mood-option.selected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.mood-emoji {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

/* Games */
.game-card {
    text-align: center;
    padding: 2rem;
    border-radius: var(--border-radius);
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.game-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.progress-ring {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
}

.progress-ring-circle {
    stroke: #e9ecef;
    stroke-width: 8;
    fill: transparent;
    transition: stroke-dasharray 0.5s ease;
}

.progress-ring-circle.progress {
    stroke: var(--primary-color);
    stroke-linecap: round;
}

/* Breathing Exercise */
.breathing-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    margin: 2rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    transition: transform 4s ease-in-out;
}

.breathing-circle.inhale {
    transform: scale(1.3);
}

.breathing-circle.exhale {
    transform: scale(0.8);
}

/* Learning Modules */
.module-card {
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.module-card:hover {
    border-left-color: var(--success-color);
}

.difficulty-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
}

.difficulty-beginner {
    background: var(--success-color);
    color: white;
}

.difficulty-intermediate {
    background: var(--warning-color);
    color: #333;
}

.difficulty-advanced {
    background: var(--danger-color);
    color: white;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Achievements */
.achievement-badge {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.achievement-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.achievement-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.achievement-unlocked {
    border: 2px solid var(--warning-color);
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
}

.achievement-locked {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* Emergency Features */
.emergency-contact-card {
    border-left: 4px solid var(--danger-color);
}

.location-sharing {
    background: linear-gradient(45deg, var(--info-color), var(--primary-color));
    color: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
}

/* Admin Dashboard */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .emergency-button-container {
        bottom: 15px;
        right: 15px;
    }
    
    .emergency-btn {
        width: 60px;
        height: 60px;
    }
    
    .chat-container {
        height: 400px;
    }
    
    .breathing-circle {
        width: 150px;
        height: 150px;
        font-size: 1.2rem;
    }
    
    .mood-scale {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .mood-option {
        flex: 1;
        min-width: 80px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.bounce {
    animation: bounce 1s ease-out;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-medium {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-strong {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
