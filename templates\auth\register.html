{% extends "base.html" %}

{% block title %}Register - Mental Wellness App{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header text-center bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus"></i> 
                        Join Our Community
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Password should be at least 8 characters long.</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="age" class="form-label">Age (Optional)</label>
                                <input type="number" class="form-control" id="age" name="age" min="13" max="120">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone (Optional)</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="emergency_contact" class="form-label">Emergency Contact Phone (Optional)</label>
                            <input type="tel" class="form-control" id="emergency_contact" name="emergency_contact">
                            <div class="form-text">This will be used for emergency alerts.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="preferred_language" class="form-label">Preferred Language</label>
                            <select class="form-select" id="preferred_language" name="preferred_language">
                                <option value="en">English</option>
                                <option value="es">Español</option>
                                <option value="fr">Français</option>
                                <option value="de">Deutsch</option>
                                <option value="it">Italiano</option>
                                <option value="pt">Português</option>
                                <option value="zh">中文</option>
                                <option value="ja">日本語</option>
                                <option value="ko">한국어</option>
                                <option value="ar">العربية</option>
                            </select>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> 
                                and <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Create Account
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Already have an account? 
                        <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                            Sign in here
                        </a>
                    </p>
                </div>
            </div>
            
            <!-- Privacy Notice -->
            <div class="alert alert-info mt-3">
                <h6 class="alert-heading">
                    <i class="fas fa-shield-alt"></i> Your Privacy Matters
                </h6>
                <p class="mb-0">
                    Your personal information is encrypted and secure. We never share your data 
                    with third parties without your explicit consent.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
