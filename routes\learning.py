from flask import Blueprint, render_template, request, jsonify, flash
from flask_login import login_required, current_user
from models import LearningModule, LearningProgress, User, db
from datetime import datetime
from googletrans import Translator
import json

learning_bp = Blueprint('learning', __name__)

# Initialize translator
translator = Translator()

def get_or_create_progress(user_id, module_id):
    """Get existing learning progress or create new one"""
    progress = LearningProgress.query.filter_by(user_id=user_id, module_id=module_id).first()
    if not progress:
        progress = LearningProgress(user_id=user_id, module_id=module_id)
        db.session.add(progress)
        db.session.commit()
    return progress

def translate_content(content, target_language):
    """Translate content to target language"""
    try:
        if target_language == 'en':
            return content
        
        translated = translator.translate(content, dest=target_language)
        return translated.text
    except Exception as e:
        return content  # Return original if translation fails

@learning_bp.route('/')
@login_required
def learning_home():
    """Learning modules dashboard"""
    # Get all active modules
    modules = LearningModule.query.filter_by(is_active=True).all()
    
    # Get user's progress for each module
    user_progress = {}
    for module in modules:
        progress = LearningProgress.query.filter_by(
            user_id=current_user.id, 
            module_id=module.id
        ).first()
        user_progress[module.id] = progress
    
    # Filter by difficulty if requested
    difficulty = request.args.get('difficulty')
    if difficulty:
        modules = [m for m in modules if m.difficulty_level == difficulty]
    
    # Filter by type if requested
    module_type = request.args.get('type')
    if module_type:
        modules = [m for m in modules if m.module_type == module_type]
    
    return render_template('learning/dashboard.html', 
                         modules=modules, 
                         user_progress=user_progress)

@learning_bp.route('/module/<int:module_id>')
@login_required
def view_module(module_id):
    """View a specific learning module"""
    module = LearningModule.query.get_or_404(module_id)
    progress = get_or_create_progress(current_user.id, module_id)
    
    # Translate content if needed
    target_lang = current_user.preferred_language or 'en'
    if target_lang != 'en' and target_lang != module.language:
        translated_title = translate_content(module.title, target_lang)
        translated_description = translate_content(module.description, target_lang)
        translated_content = translate_content(module.content, target_lang)
    else:
        translated_title = module.title
        translated_description = module.description
        translated_content = module.content
    
    return render_template('learning/module.html', 
                         module=module,
                         progress=progress,
                         translated_title=translated_title,
                         translated_description=translated_description,
                         translated_content=translated_content)

@learning_bp.route('/module/<int:module_id>/start', methods=['POST'])
@login_required
def start_module(module_id):
    """Start learning a module"""
    module = LearningModule.query.get_or_404(module_id)
    progress = get_or_create_progress(current_user.id, module_id)
    
    if not progress.started_at:
        progress.started_at = datetime.utcnow()
        db.session.commit()
    
    return jsonify({'status': 'started', 'module_id': module_id})

@learning_bp.route('/module/<int:module_id>/progress', methods=['POST'])
@login_required
def update_progress(module_id):
    """Update learning progress"""
    data = request.json
    progress_percentage = data.get('progress', 0)
    
    module = LearningModule.query.get_or_404(module_id)
    progress = get_or_create_progress(current_user.id, module_id)
    
    # Update progress
    progress.progress_percentage = min(100, max(0, progress_percentage))
    
    # Mark as completed if 100%
    if progress.progress_percentage == 100 and not progress.completed_at:
        progress.completed_at = datetime.utcnow()
        
        # Award points for completion
        points_earned = 20  # Base points for module completion
        if module.difficulty_level == 'intermediate':
            points_earned = 30
        elif module.difficulty_level == 'advanced':
            points_earned = 40
        
        current_user.points += points_earned
        
        # Check for level up
        new_level = (current_user.points // 100) + 1
        level_up = new_level > current_user.level
        current_user.level = new_level
        
        db.session.commit()
        
        return jsonify({
            'status': 'completed',
            'points_earned': points_earned,
            'level_up': level_up,
            'new_level': current_user.level
        })
    
    db.session.commit()
    return jsonify({'status': 'updated', 'progress': progress.progress_percentage})

@learning_bp.route('/module/<int:module_id>/quiz', methods=['GET', 'POST'])
@login_required
def module_quiz(module_id):
    """Handle module quiz"""
    module = LearningModule.query.get_or_404(module_id)
    progress = get_or_create_progress(current_user.id, module_id)
    
    if request.method == 'GET':
        # Generate quiz questions based on module content
        # This is a simplified version - in a real app, you'd have a proper quiz system
        quiz_questions = generate_quiz_questions(module)
        return render_template('learning/quiz.html', 
                             module=module, 
                             questions=quiz_questions)
    
    # Handle quiz submission
    answers = request.json.get('answers', {})
    score = calculate_quiz_score(module, answers)
    
    progress.quiz_score = score
    
    # Award bonus points for good quiz performance
    if score >= 80:
        bonus_points = 10
        current_user.points += bonus_points
        db.session.commit()
        
        return jsonify({
            'score': score,
            'bonus_points': bonus_points,
            'message': 'Excellent work! Bonus points awarded.'
        })
    
    db.session.commit()
    return jsonify({'score': score})

def generate_quiz_questions(module):
    """Generate quiz questions for a module (simplified)"""
    # This is a basic implementation - in a real app, you'd have a proper question bank
    questions = []
    
    if 'anxiety' in module.title.lower():
        questions = [
            {
                'question': 'What is a common physical symptom of anxiety?',
                'options': ['Increased heart rate', 'Improved focus', 'Better sleep', 'Increased appetite'],
                'correct': 0
            },
            {
                'question': 'Which technique can help manage anxiety?',
                'options': ['Avoiding all stressful situations', 'Deep breathing exercises', 'Drinking caffeine', 'Staying up late'],
                'correct': 1
            }
        ]
    elif 'depression' in module.title.lower():
        questions = [
            {
                'question': 'What is a key symptom of depression?',
                'options': ['Excessive energy', 'Persistent sadness', 'Improved concentration', 'Increased social activity'],
                'correct': 1
            },
            {
                'question': 'Which activity can help with depression?',
                'options': ['Isolating yourself', 'Regular exercise', 'Avoiding sunlight', 'Skipping meals'],
                'correct': 1
            }
        ]
    else:
        # Generic mental health questions
        questions = [
            {
                'question': 'What is an important aspect of mental wellness?',
                'options': ['Ignoring emotions', 'Self-care practices', 'Avoiding help', 'Working constantly'],
                'correct': 1
            },
            {
                'question': 'When should someone seek professional help?',
                'options': ['Never', 'Only in emergencies', 'When symptoms interfere with daily life', 'Only when forced'],
                'correct': 2
            }
        ]
    
    return questions

def calculate_quiz_score(module, answers):
    """Calculate quiz score based on answers"""
    questions = generate_quiz_questions(module)
    correct_answers = 0
    
    for i, question in enumerate(questions):
        user_answer = answers.get(str(i))
        if user_answer is not None and int(user_answer) == question['correct']:
            correct_answers += 1
    
    return int((correct_answers / len(questions)) * 100) if questions else 0

@learning_bp.route('/progress')
@login_required
def learning_progress():
    """View overall learning progress"""
    # Get all user's learning progress
    progress_records = LearningProgress.query.filter_by(user_id=current_user.id).all()
    
    # Get modules for each progress record
    progress_with_modules = []
    for progress in progress_records:
        module = LearningModule.query.get(progress.module_id)
        if module:
            progress_with_modules.append({
                'progress': progress,
                'module': module
            })
    
    # Calculate statistics
    total_modules = LearningModule.query.filter_by(is_active=True).count()
    completed_modules = len([p for p in progress_records if p.completed_at])
    in_progress_modules = len([p for p in progress_records if p.started_at and not p.completed_at])
    
    avg_quiz_score = 0
    quiz_scores = [p.quiz_score for p in progress_records if p.quiz_score is not None]
    if quiz_scores:
        avg_quiz_score = sum(quiz_scores) / len(quiz_scores)
    
    stats = {
        'total_modules': total_modules,
        'completed_modules': completed_modules,
        'in_progress_modules': in_progress_modules,
        'completion_rate': (completed_modules / total_modules * 100) if total_modules > 0 else 0,
        'avg_quiz_score': avg_quiz_score
    }
    
    return render_template('learning/progress.html', 
                         progress_records=progress_with_modules,
                         stats=stats)

@learning_bp.route('/search')
@login_required
def search_modules():
    """Search learning modules"""
    query = request.args.get('q', '')
    difficulty = request.args.get('difficulty')
    module_type = request.args.get('type')
    
    modules = LearningModule.query.filter_by(is_active=True)
    
    if query:
        modules = modules.filter(
            LearningModule.title.contains(query) | 
            LearningModule.description.contains(query)
        )
    
    if difficulty:
        modules = modules.filter_by(difficulty_level=difficulty)
    
    if module_type:
        modules = modules.filter_by(module_type=module_type)
    
    modules = modules.all()
    
    # Get user progress for found modules
    user_progress = {}
    for module in modules:
        progress = LearningProgress.query.filter_by(
            user_id=current_user.id, 
            module_id=module.id
        ).first()
        user_progress[module.id] = progress
    
    return render_template('learning/search_results.html', 
                         modules=modules, 
                         user_progress=user_progress,
                         query=query)
