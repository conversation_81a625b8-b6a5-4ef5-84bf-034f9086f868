// Mental Wellness App - Main JavaScript

// Global variables
let emergencyTimer = null;
let emergencyPressed = false;
let currentLocation = null;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize emergency button
    initializeEmergencyButton();
    
    // Initialize geolocation
    initializeGeolocation();
    
    // Initialize notifications
    initializeNotifications();
    
    // Initialize tooltips and popovers
    initializeBootstrapComponents();
    
    // Initialize page-specific features
    initializePageFeatures();
}

// Emergency Button Functionality
function initializeEmergencyButton() {
    const emergencyBtn = document.getElementById('emergencyBtn');
    if (!emergencyBtn) return;
    
    let pressTimer = null;
    let pressStartTime = null;
    
    // Mouse events
    emergencyBtn.addEventListener('mousedown', startEmergencyPress);
    emergencyBtn.addEventListener('mouseup', cancelEmergencyPress);
    emergencyBtn.addEventListener('mouseleave', cancelEmergencyPress);
    
    // Touch events for mobile
    emergencyBtn.addEventListener('touchstart', startEmergencyPress);
    emergencyBtn.addEventListener('touchend', cancelEmergencyPress);
    emergencyBtn.addEventListener('touchcancel', cancelEmergencyPress);
    
    function startEmergencyPress(e) {
        e.preventDefault();
        if (emergencyPressed) return;
        
        emergencyPressed = true;
        pressStartTime = Date.now();
        emergencyBtn.classList.add('pressed');
        
        // Show countdown
        showEmergencyCountdown();
        
        // Set timer for 3 seconds
        pressTimer = setTimeout(() => {
            triggerEmergencyAlert();
        }, 3000);
    }
    
    function cancelEmergencyPress(e) {
        e.preventDefault();
        if (!emergencyPressed) return;
        
        emergencyPressed = false;
        emergencyBtn.classList.remove('pressed');
        
        if (pressTimer) {
            clearTimeout(pressTimer);
            pressTimer = null;
        }
        
        hideEmergencyCountdown();
    }
}

function showEmergencyCountdown() {
    // Create countdown overlay
    const overlay = document.createElement('div');
    overlay.id = 'emergencyCountdown';
    overlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
    overlay.style.backgroundColor = 'rgba(220, 53, 69, 0.9)';
    overlay.style.zIndex = '9999';
    overlay.style.color = 'white';
    overlay.innerHTML = `
        <div class="text-center">
            <h1 class="display-1 mb-3" id="countdownNumber">3</h1>
            <h3>Emergency Alert Activating...</h3>
            <p>Release to cancel</p>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Countdown animation
    let count = 3;
    const countdownInterval = setInterval(() => {
        count--;
        const numberEl = document.getElementById('countdownNumber');
        if (numberEl) {
            numberEl.textContent = count;
            if (count === 0) {
                numberEl.textContent = 'SENDING!';
                clearInterval(countdownInterval);
            }
        }
    }, 1000);
}

function hideEmergencyCountdown() {
    const overlay = document.getElementById('emergencyCountdown');
    if (overlay) {
        overlay.remove();
    }
}

function triggerEmergencyAlert() {
    hideEmergencyCountdown();
    
    // Get current location and send emergency alert
    if (currentLocation) {
        sendEmergencyAlert(currentLocation.latitude, currentLocation.longitude);
    } else {
        // Try to get location quickly
        navigator.geolocation.getCurrentPosition(
            (position) => {
                sendEmergencyAlert(position.coords.latitude, position.coords.longitude);
            },
            () => {
                sendEmergencyAlert(null, null);
            },
            { timeout: 5000, enableHighAccuracy: false }
        );
    }
}

function sendEmergencyAlert(latitude, longitude) {
    const alertData = {
        latitude: latitude,
        longitude: longitude,
        message: 'Emergency alert triggered from panic button'
    };
    
    fetch('/emergency/panic', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(alertData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showNotification('Emergency alert sent successfully!', 'success');
            showEmergencyConfirmation(data);
        } else {
            showNotification('Failed to send emergency alert. Please call 911.', 'error');
        }
    })
    .catch(error => {
        console.error('Emergency alert error:', error);
        showNotification('Emergency system error. Please call 911 immediately.', 'error');
    });
}

function showEmergencyConfirmation(data) {
    const modal = document.createElement('div');
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-check-circle"></i> Emergency Alert Sent
                    </h5>
                </div>
                <div class="modal-body">
                    <p><strong>Your emergency contacts have been notified:</strong></p>
                    <ul>
                        <li>${data.contacts_notified} contacts successfully notified</li>
                        ${data.failed_contacts.length > 0 ? `<li class="text-warning">Failed to reach: ${data.failed_contacts.join(', ')}</li>` : ''}
                    </ul>
                    <div class="alert alert-info mt-3">
                        <strong>Remember:</strong> If this is a life-threatening emergency, call 911 immediately.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="this.closest('.modal').remove()">
                        OK
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (modal.parentNode) {
            modal.remove();
        }
    }, 10000);
}

// Geolocation
function initializeGeolocation() {
    if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };
            },
            (error) => {
                console.log('Geolocation error:', error);
            },
            { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
        );
        
        // Watch position for updates
        navigator.geolocation.watchPosition(
            (position) => {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };
            },
            null,
            { enableHighAccuracy: false, timeout: 60000, maximumAge: 600000 }
        );
    }
}

// Notifications
function initializeNotifications() {
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
}

function showNotification(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to toast container or create one
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove from DOM after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Bootstrap Components
function initializeBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Page-specific Features
function initializePageFeatures() {
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('/chat')) {
        initializeChatFeatures();
    } else if (currentPage.includes('/games')) {
        initializeGameFeatures();
    } else if (currentPage.includes('/learning')) {
        initializeLearningFeatures();
    }
}

// Chat Features
function initializeChatFeatures() {
    const chatContainer = document.getElementById('chatContainer');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    
    if (messageInput && sendButton) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });
        
        sendButton.addEventListener('click', sendChatMessage);
    }
}

function sendChatMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    messageInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    // Send to server
    fetch('/chat/send_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: getCurrentSessionId(),
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();
        addMessageToChat(data.response, 'bot');
        
        if (data.crisis_detected) {
            showCrisisAlert();
        }
    })
    .catch(error => {
        hideTypingIndicator();
        addMessageToChat('Sorry, I\'m having trouble connecting right now. Please try again.', 'bot');
    });
}

function addMessageToChat(message, sender) {
    const chatContainer = document.getElementById('chatContainer');
    const messageEl = document.createElement('div');
    messageEl.className = `chat-message ${sender}`;
    
    const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageEl.innerHTML = `
        <div class="message-bubble ${sender}">
            ${message}
            <div class="message-time">${time}</div>
        </div>
    `;
    
    chatContainer.appendChild(messageEl);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function showTypingIndicator() {
    const chatContainer = document.getElementById('chatContainer');
    const typingEl = document.createElement('div');
    typingEl.id = 'typingIndicator';
    typingEl.className = 'chat-message bot';
    typingEl.innerHTML = `
        <div class="message-bubble bot">
            <div class="typing-dots">
                <span></span><span></span><span></span>
            </div>
        </div>
    `;
    
    chatContainer.appendChild(typingEl);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function hideTypingIndicator() {
    const typingEl = document.getElementById('typingIndicator');
    if (typingEl) {
        typingEl.remove();
    }
}

function getCurrentSessionId() {
    // This would be set when starting a chat session
    return window.currentSessionId || null;
}

function showCrisisAlert() {
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show';
    alert.innerHTML = `
        <h4 class="alert-heading">
            <i class="fas fa-exclamation-triangle"></i> Crisis Support Available
        </h4>
        <p>I'm concerned about you. Please consider reaching out for immediate help:</p>
        <ul>
            <li><strong>Emergency:</strong> 911</li>
            <li><strong>Crisis Text Line:</strong> Text HOME to 741741</li>
            <li><strong>National Suicide Prevention Lifeline:</strong> 988</li>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const chatContainer = document.getElementById('chatContainer');
    chatContainer.parentNode.insertBefore(alert, chatContainer);
}

// Game Features
function initializeGameFeatures() {
    // Initialize breathing exercise
    initializeBreathingExercise();
    
    // Initialize mood tracker
    initializeMoodTracker();
}

function initializeBreathingExercise() {
    const breathingCircle = document.getElementById('breathingCircle');
    const startBtn = document.getElementById('startBreathing');
    
    if (startBtn) {
        startBtn.addEventListener('click', startBreathingExercise);
    }
}

function startBreathingExercise() {
    const breathingCircle = document.getElementById('breathingCircle');
    const instructionText = document.getElementById('breathingInstruction');
    
    if (!breathingCircle || !instructionText) return;
    
    let cycles = 0;
    const maxCycles = 10;
    
    function breathingCycle() {
        if (cycles >= maxCycles) {
            completeBreathingExercise(cycles);
            return;
        }
        
        // Inhale phase
        instructionText.textContent = 'Breathe In...';
        breathingCircle.classList.add('inhale');
        breathingCircle.classList.remove('exhale');
        
        setTimeout(() => {
            // Hold phase
            instructionText.textContent = 'Hold...';
            
            setTimeout(() => {
                // Exhale phase
                instructionText.textContent = 'Breathe Out...';
                breathingCircle.classList.add('exhale');
                breathingCircle.classList.remove('inhale');
                
                setTimeout(() => {
                    cycles++;
                    breathingCycle();
                }, 4000);
            }, 2000);
        }, 4000);
    }
    
    breathingCycle();
}

function completeBreathingExercise(cycles) {
    const duration = cycles * 10; // Approximate duration in seconds
    
    fetch('/games/breathing/complete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            duration: duration,
            cycles: cycles
        })
    })
    .then(response => response.json())
    .then(data => {
        showNotification(`Great job! You earned ${data.points_earned} points!`, 'success');
        if (data.level_up) {
            showLevelUpNotification(data.new_level);
        }
    });
}

function showLevelUpNotification(newLevel) {
    const modal = document.createElement('div');
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-trophy"></i> Level Up!
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <h2 class="text-warning">🎉</h2>
                    <h4>Congratulations!</h4>
                    <p>You've reached <strong>Level ${newLevel}</strong>!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" onclick="this.closest('.modal').remove()">
                        Awesome!
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    setTimeout(() => {
        if (modal.parentNode) {
            modal.remove();
        }
    }, 5000);
}

// Mood Tracker
function initializeMoodTracker() {
    const moodOptions = document.querySelectorAll('.mood-option');
    
    moodOptions.forEach(option => {
        option.addEventListener('click', function() {
            moodOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
}

// Learning Features
function initializeLearningFeatures() {
    // Initialize progress tracking
    initializeProgressTracking();
}

function initializeProgressTracking() {
    // Track reading progress
    let scrollProgress = 0;
    const content = document.getElementById('moduleContent');
    
    if (content) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.offsetHeight;
            const winHeight = window.innerHeight;
            const scrollPercent = scrollTop / (docHeight - winHeight);
            const scrollPercentRounded = Math.round(scrollPercent * 100);
            
            if (scrollPercentRounded > scrollProgress) {
                scrollProgress = scrollPercentRounded;
                updateLearningProgress(scrollProgress);
            }
        });
    }
}

function updateLearningProgress(progress) {
    const moduleId = getModuleId();
    if (!moduleId) return;
    
    fetch(`/learning/module/${moduleId}/progress`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            progress: progress
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'completed') {
            showNotification(`Module completed! You earned ${data.points_earned} points!`, 'success');
            if (data.level_up) {
                showLevelUpNotification(data.new_level);
            }
        }
    });
}

function getModuleId() {
    // Extract module ID from URL or data attribute
    const match = window.location.pathname.match(/\/module\/(\d+)/);
    return match ? match[1] : null;
}

// Utility Functions
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
