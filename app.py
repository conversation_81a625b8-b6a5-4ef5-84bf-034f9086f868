from flask import Flask, render_template, redirect, url_for, flash, request
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, login_required, current_user
from werkzeug.security import generate_password_hash
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///wellness_app.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
from models import db
db.init_app(app)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'

# Import models after db initialization
from models import User, ChatSession, EmergencyContact, GameProgress, LearningModule

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Import and register blueprints
from routes.auth import auth_bp
from routes.chat import chat_bp
from routes.emergency import emergency_bp
from routes.games import games_bp
from routes.learning import learning_bp
from routes.admin import admin_bp

app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(chat_bp, url_prefix='/chat')
app.register_blueprint(emergency_bp, url_prefix='/emergency')
app.register_blueprint(games_bp, url_prefix='/games')
app.register_blueprint(learning_bp, url_prefix='/learning')
app.register_blueprint(admin_bp, url_prefix='/admin')

# Main routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return render_template('dashboard.html')
    return render_template('index.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

# Create database tables
def create_tables():
    with app.app_context():
        db.create_all()

        # Create default admin user if it doesn't exist
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                is_admin=True
            )
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: <EMAIL> / admin123")

if __name__ == '__main__':
    # Create tables before running the app
    create_tables()
    app.run(debug=True, host='0.0.0.0', port=5000)
