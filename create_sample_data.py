#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sample data for the Mental Wellness App
Run this after the database is created to populate it with sample content.
"""

from app import app, db
from models import LearningModule
from datetime import datetime

def create_sample_learning_modules():
    """Create sample learning modules"""
    
    modules = [
        {
            'title': 'Understanding Anxiety: A Beginner\'s Guide',
            'description': 'Learn about anxiety disorders, their symptoms, and basic coping strategies.',
            'content': '''
            <h3>What is Anxiety?</h3>
            <p>Anxiety is a natural response to stress and can be beneficial in some situations. However, when anxiety becomes excessive, it may interfere with daily activities.</p>
            
            <h4>Common Symptoms of Anxiety:</h4>
            <ul>
                <li>Feeling nervous, restless, or tense</li>
                <li>Having a sense of impending danger or panic</li>
                <li>Increased heart rate</li>
                <li>Rapid breathing (hyperventilation)</li>
                <li>Sweating and trembling</li>
                <li>Trouble concentrating</li>
                <li>Difficulty sleeping</li>
            </ul>
            
            <h4>Basic Coping Strategies:</h4>
            <ol>
                <li><strong>Deep Breathing:</strong> Practice slow, deep breaths to calm your nervous system</li>
                <li><strong>Progressive Muscle Relaxation:</strong> Tense and release different muscle groups</li>
                <li><strong>Mindfulness:</strong> Focus on the present moment without judgment</li>
                <li><strong>Regular Exercise:</strong> Physical activity can reduce anxiety symptoms</li>
                <li><strong>Healthy Sleep Habits:</strong> Maintain a consistent sleep schedule</li>
            </ol>
            
            <h4>When to Seek Help:</h4>
            <p>Consider professional help if anxiety:</p>
            <ul>
                <li>Interferes with work, school, or relationships</li>
                <li>Causes you to avoid certain situations</li>
                <li>Leads to panic attacks</li>
                <li>Affects your physical health</li>
            </ul>
            
            <div class="alert alert-info">
                <strong>Remember:</strong> Anxiety is treatable, and you don't have to face it alone. Professional help is available and effective.
            </div>
            ''',
            'module_type': 'article',
            'difficulty_level': 'beginner',
            'estimated_time': 15
        },
        {
            'title': 'Depression: Recognizing the Signs',
            'description': 'Understanding depression symptoms and available treatment options.',
            'content': '''
            <h3>Understanding Depression</h3>
            <p>Depression is more than just feeling sad or going through a rough patch. It's a serious mental health condition that affects how you feel, think, and handle daily activities.</p>
            
            <h4>Signs and Symptoms:</h4>
            <ul>
                <li>Persistent sad, anxious, or "empty" mood</li>
                <li>Loss of interest in activities once enjoyed</li>
                <li>Significant weight loss or gain</li>
                <li>Sleeping too much or too little</li>
                <li>Fatigue and decreased energy</li>
                <li>Feelings of worthlessness or guilt</li>
                <li>Difficulty concentrating or making decisions</li>
                <li>Thoughts of death or suicide</li>
            </ul>
            
            <h4>Types of Depression:</h4>
            <ul>
                <li><strong>Major Depression:</strong> Severe symptoms that interfere with daily life</li>
                <li><strong>Persistent Depressive Disorder:</strong> Long-term depression lasting 2+ years</li>
                <li><strong>Seasonal Affective Disorder:</strong> Depression related to seasonal changes</li>
                <li><strong>Postpartum Depression:</strong> Depression after childbirth</li>
            </ul>
            
            <h4>Treatment Options:</h4>
            <ol>
                <li><strong>Therapy:</strong> Cognitive Behavioral Therapy (CBT), Interpersonal Therapy</li>
                <li><strong>Medication:</strong> Antidepressants prescribed by healthcare providers</li>
                <li><strong>Lifestyle Changes:</strong> Exercise, healthy diet, regular sleep</li>
                <li><strong>Support Groups:</strong> Connecting with others who understand</li>
            </ol>
            
            <div class="alert alert-warning">
                <strong>Crisis Resources:</strong> If you're having thoughts of suicide, please reach out immediately:
                <ul>
                    <li>National Suicide Prevention Lifeline: 988</li>
                    <li>Crisis Text Line: Text HOME to 741741</li>
                    <li>Emergency Services: 911</li>
                </ul>
            </div>
            ''',
            'module_type': 'article',
            'difficulty_level': 'beginner',
            'estimated_time': 20
        },
        {
            'title': 'Stress Management Techniques',
            'description': 'Practical strategies for managing stress in daily life.',
            'content': '''
            <h3>Understanding Stress</h3>
            <p>Stress is your body's response to challenges or demands. While some stress is normal and can be motivating, chronic stress can harm your physical and mental health.</p>
            
            <h4>Quick Stress Relief Techniques:</h4>
            <ol>
                <li><strong>4-7-8 Breathing:</strong> Inhale for 4, hold for 7, exhale for 8</li>
                <li><strong>5-4-3-2-1 Grounding:</strong> Name 5 things you see, 4 you hear, 3 you touch, 2 you smell, 1 you taste</li>
                <li><strong>Progressive Muscle Relaxation:</strong> Tense and release muscle groups</li>
                <li><strong>Visualization:</strong> Imagine a peaceful, calming place</li>
                <li><strong>Quick Walk:</strong> Even 5 minutes of movement can help</li>
            </ol>
            
            <h4>Long-term Stress Management:</h4>
            <ul>
                <li><strong>Regular Exercise:</strong> Aim for 30 minutes most days</li>
                <li><strong>Healthy Diet:</strong> Limit caffeine, alcohol, and processed foods</li>
                <li><strong>Quality Sleep:</strong> 7-9 hours per night</li>
                <li><strong>Time Management:</strong> Prioritize tasks and set realistic goals</li>
                <li><strong>Social Support:</strong> Maintain relationships with family and friends</li>
                <li><strong>Hobbies:</strong> Engage in activities you enjoy</li>
            </ul>
            
            <h4>When Stress Becomes a Problem:</h4>
            <p>Seek professional help if you experience:</p>
            <ul>
                <li>Persistent anxiety or worry</li>
                <li>Changes in sleep or appetite</li>
                <li>Difficulty concentrating</li>
                <li>Increased use of alcohol or drugs</li>
                <li>Physical symptoms like headaches or stomach problems</li>
            </ul>
            
            <div class="alert alert-success">
                <strong>Practice Tip:</strong> Try one stress management technique daily for a week and notice how it affects your mood and energy levels.
            </div>
            ''',
            'module_type': 'interactive',
            'difficulty_level': 'beginner',
            'estimated_time': 25
        },
        {
            'title': 'Building Emotional Resilience',
            'description': 'Develop skills to bounce back from challenges and adapt to change.',
            'content': '''
            <h3>What is Emotional Resilience?</h3>
            <p>Emotional resilience is the ability to adapt to stressful situations and bounce back from adversity. It's not about avoiding difficulties, but learning to navigate them effectively.</p>
            
            <h4>Key Components of Resilience:</h4>
            <ul>
                <li><strong>Self-awareness:</strong> Understanding your emotions and reactions</li>
                <li><strong>Emotional regulation:</strong> Managing intense emotions effectively</li>
                <li><strong>Optimism:</strong> Maintaining hope and positive outlook</li>
                <li><strong>Flexibility:</strong> Adapting to changing circumstances</li>
                <li><strong>Social connection:</strong> Building and maintaining supportive relationships</li>
            </ul>
            
            <h4>Building Resilience Skills:</h4>
            <ol>
                <li><strong>Practice Self-Compassion:</strong> Treat yourself with kindness during difficult times</li>
                <li><strong>Develop Problem-Solving Skills:</strong> Break challenges into manageable steps</li>
                <li><strong>Cultivate Gratitude:</strong> Regularly acknowledge positive aspects of your life</li>
                <li><strong>Build Meaning:</strong> Connect with your values and purpose</li>
                <li><strong>Learn from Experience:</strong> Reflect on past challenges and how you overcame them</li>
            </ol>
            
            <h4>Resilience-Building Activities:</h4>
            <ul>
                <li>Keep a gratitude journal</li>
                <li>Practice mindfulness meditation</li>
                <li>Engage in regular physical activity</li>
                <li>Volunteer for causes you care about</li>
                <li>Learn new skills or hobbies</li>
                <li>Maintain social connections</li>
            </ul>
            
            <h4>The Growth Mindset:</h4>
            <p>Embrace challenges as opportunities to learn and grow. Remember that setbacks are temporary and don't define your worth or capabilities.</p>
            
            <div class="alert alert-info">
                <strong>Reflection Exercise:</strong> Think of a recent challenge you faced. What strengths did you use to get through it? How can you apply these strengths to future difficulties?
            </div>
            ''',
            'module_type': 'interactive',
            'difficulty_level': 'intermediate',
            'estimated_time': 30
        },
        {
            'title': 'Mindfulness and Meditation Basics',
            'description': 'Introduction to mindfulness practices for mental wellness.',
            'content': '''
            <h3>What is Mindfulness?</h3>
            <p>Mindfulness is the practice of purposefully paying attention to the present moment without judgment. It's about being fully aware of where you are and what you're doing.</p>
            
            <h4>Benefits of Mindfulness:</h4>
            <ul>
                <li>Reduced stress and anxiety</li>
                <li>Improved focus and concentration</li>
                <li>Better emotional regulation</li>
                <li>Enhanced self-awareness</li>
                <li>Improved sleep quality</li>
                <li>Greater life satisfaction</li>
            </ul>
            
            <h4>Simple Mindfulness Exercises:</h4>
            <ol>
                <li><strong>Mindful Breathing:</strong>
                    <ul>
                        <li>Sit comfortably and close your eyes</li>
                        <li>Focus on your natural breath</li>
                        <li>When your mind wanders, gently return to your breath</li>
                        <li>Start with 5 minutes daily</li>
                    </ul>
                </li>
                <li><strong>Body Scan:</strong>
                    <ul>
                        <li>Lie down comfortably</li>
                        <li>Focus attention on different parts of your body</li>
                        <li>Notice sensations without trying to change them</li>
                        <li>Move from toes to head systematically</li>
                    </ul>
                </li>
                <li><strong>Mindful Walking:</strong>
                    <ul>
                        <li>Walk slowly and deliberately</li>
                        <li>Focus on the sensation of your feet touching the ground</li>
                        <li>Notice your surroundings without judgment</li>
                        <li>Can be done indoors or outdoors</li>
                    </ul>
                </li>
            </ol>
            
            <h4>Common Challenges and Solutions:</h4>
            <ul>
                <li><strong>"My mind won't stop thinking":</strong> This is normal! The goal isn't to stop thoughts but to notice them without getting caught up in them.</li>
                <li><strong>"I don't have time":</strong> Start with just 2-3 minutes. Even brief practice is beneficial.</li>
                <li><strong>"I can't sit still":</strong> Try walking meditation or mindful movement instead.</li>
                <li><strong>"I fall asleep":</strong> Practice with eyes slightly open or in a more upright position.</li>
            </ul>
            
            <div class="alert alert-success">
                <strong>Getting Started:</strong> Choose one mindfulness exercise and practice it for 5 minutes daily for one week. Notice any changes in your stress levels or mood.
            </div>
            ''',
            'module_type': 'interactive',
            'difficulty_level': 'beginner',
            'estimated_time': 20
        }
    ]
    
    for module_data in modules:
        # Check if module already exists
        existing = LearningModule.query.filter_by(title=module_data['title']).first()
        if not existing:
            module = LearningModule(**module_data)
            db.session.add(module)
    
    db.session.commit()
    print(f"Created {len(modules)} sample learning modules")

def main():
    """Main function to create sample data"""
    with app.app_context():
        print("Creating sample data...")
        create_sample_learning_modules()
        print("Sample data creation completed!")

if __name__ == '__main__':
    main()
