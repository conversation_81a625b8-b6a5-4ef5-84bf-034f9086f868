from flask import Blueprint, render_template, request, jsonify, flash
from flask_login import login_required, current_user
from models import Emergency<PERSON><PERSON><PERSON>, EmergencyAlert, db
from twilio.rest import Client
import os
from datetime import datetime

emergency_bp = Blueprint('emergency', __name__)

# Configure Twilio
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')
TWILIO_PHONE_NUMBER = os.environ.get('TWILIO_PHONE_NUMBER')

def send_emergency_sms(phone_number, message):
    """Send emergency SMS using Twilio"""
    try:
        if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER]):
            return False, "Twilio not configured"
        
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        message = client.messages.create(
            body=message,
            from_=TWILIO_PHONE_NUMBER,
            to=phone_number
        )
        
        return True, message.sid
    except Exception as e:
        return False, str(e)

@emergency_bp.route('/')
@login_required
def emergency_home():
    """Emergency dashboard"""
    contacts = EmergencyContact.query.filter_by(user_id=current_user.id).all()
    recent_alerts = EmergencyAlert.query.filter_by(user_id=current_user.id)\
                                       .order_by(EmergencyAlert.timestamp.desc())\
                                       .limit(5).all()
    return render_template('emergency/dashboard.html', contacts=contacts, recent_alerts=recent_alerts)

@emergency_bp.route('/panic', methods=['POST'])
@login_required
def panic_button():
    """Handle panic button press"""
    data = request.json
    location_lat = data.get('latitude')
    location_lng = data.get('longitude')
    custom_message = data.get('message', '')
    
    # Create emergency alert
    alert = EmergencyAlert(
        user_id=current_user.id,
        alert_type='panic',
        location_lat=location_lat,
        location_lng=location_lng,
        message=custom_message
    )
    db.session.add(alert)
    db.session.commit()
    
    # Get emergency contacts
    contacts = EmergencyContact.query.filter_by(user_id=current_user.id).all()
    
    # Prepare emergency message
    user_name = f"{current_user.first_name} {current_user.last_name}" if current_user.first_name else current_user.username
    base_message = f"🚨 EMERGENCY ALERT: {user_name} has activated their panic button."
    
    if location_lat and location_lng:
        base_message += f" Location: https://maps.google.com/?q={location_lat},{location_lng}"
    
    if custom_message:
        base_message += f" Message: {custom_message}"
    
    base_message += " Please check on them immediately."
    
    # Send SMS to all emergency contacts
    sent_count = 0
    failed_contacts = []
    
    for contact in contacts:
        success, result = send_emergency_sms(contact.phone, base_message)
        if success:
            sent_count += 1
        else:
            failed_contacts.append(contact.name)
    
    return jsonify({
        'status': 'success',
        'alert_id': alert.id,
        'contacts_notified': sent_count,
        'failed_contacts': failed_contacts
    })

@emergency_bp.route('/location_share', methods=['POST'])
@login_required
def share_location():
    """Share current location with emergency contacts"""
    data = request.json
    location_lat = data.get('latitude')
    location_lng = data.get('longitude')
    message = data.get('message', 'Sharing my current location with you.')
    
    # Create location alert
    alert = EmergencyAlert(
        user_id=current_user.id,
        alert_type='location',
        location_lat=location_lat,
        location_lng=location_lng,
        message=message
    )
    db.session.add(alert)
    db.session.commit()
    
    # Get emergency contacts
    contacts = EmergencyContact.query.filter_by(user_id=current_user.id).all()
    
    # Prepare location message
    user_name = f"{current_user.first_name} {current_user.last_name}" if current_user.first_name else current_user.username
    location_message = f"📍 {user_name} is sharing their location: https://maps.google.com/?q={location_lat},{location_lng}"
    
    if message != 'Sharing my current location with you.':
        location_message += f" Message: {message}"
    
    # Send SMS to all emergency contacts
    sent_count = 0
    for contact in contacts:
        success, result = send_emergency_sms(contact.phone, location_message)
        if success:
            sent_count += 1
    
    return jsonify({
        'status': 'success',
        'alert_id': alert.id,
        'contacts_notified': sent_count
    })

@emergency_bp.route('/contacts')
@login_required
def manage_contacts():
    """Manage emergency contacts"""
    contacts = EmergencyContact.query.filter_by(user_id=current_user.id).all()
    return render_template('emergency/contacts.html', contacts=contacts)

@emergency_bp.route('/contacts/add', methods=['POST'])
@login_required
def add_contact():
    """Add new emergency contact"""
    name = request.form['name']
    phone = request.form['phone']
    relationship = request.form['relationship']
    is_primary = bool(request.form.get('is_primary'))
    
    # If this is set as primary, remove primary from others
    if is_primary:
        EmergencyContact.query.filter_by(user_id=current_user.id, is_primary=True)\
                             .update({'is_primary': False})
    
    contact = EmergencyContact(
        user_id=current_user.id,
        name=name,
        phone=phone,
        relationship=relationship,
        is_primary=is_primary
    )
    
    db.session.add(contact)
    db.session.commit()
    
    flash('Emergency contact added successfully!', 'success')
    return redirect(url_for('emergency.manage_contacts'))

@emergency_bp.route('/contacts/<int:contact_id>/delete', methods=['POST'])
@login_required
def delete_contact(contact_id):
    """Delete emergency contact"""
    contact = EmergencyContact.query.filter_by(id=contact_id, user_id=current_user.id).first_or_404()
    db.session.delete(contact)
    db.session.commit()
    
    flash('Emergency contact deleted successfully!', 'success')
    return redirect(url_for('emergency.manage_contacts'))

@emergency_bp.route('/alerts')
@login_required
def alert_history():
    """View emergency alert history"""
    alerts = EmergencyAlert.query.filter_by(user_id=current_user.id)\
                                 .order_by(EmergencyAlert.timestamp.desc()).all()
    return render_template('emergency/alerts.html', alerts=alerts)

@emergency_bp.route('/test_alert', methods=['POST'])
@login_required
def test_alert():
    """Send test alert to verify emergency system"""
    contacts = EmergencyContact.query.filter_by(user_id=current_user.id).all()
    
    if not contacts:
        return jsonify({'error': 'No emergency contacts configured'}), 400
    
    user_name = f"{current_user.first_name} {current_user.last_name}" if current_user.first_name else current_user.username
    test_message = f"🧪 TEST ALERT: This is a test of {user_name}'s emergency alert system. No action needed."
    
    sent_count = 0
    for contact in contacts:
        success, result = send_emergency_sms(contact.phone, test_message)
        if success:
            sent_count += 1
    
    return jsonify({
        'status': 'success',
        'contacts_notified': sent_count,
        'total_contacts': len(contacts)
    })
