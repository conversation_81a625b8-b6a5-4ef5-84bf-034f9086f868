from flask import Blueprint, render_template, request, jsonify, flash
from flask_login import login_required, current_user
from models import ChatSession, ChatMessage, db
from datetime import datetime
import openai
import os
from textblob import TextBlob
import json

chat_bp = Blueprint('chat', __name__)

# Configure OpenAI
openai.api_key = os.environ.get('OPENAI_API_KEY')

# Crisis keywords for detection
CRISIS_KEYWORDS = [
    'suicide', 'kill myself', 'end it all', 'hurt myself', 'self harm',
    'want to die', 'no point living', 'better off dead', 'harm myself'
]

def analyze_sentiment(text):
    """Analyze sentiment of text using TextBlob"""
    try:
        blob = TextBlob(text)
        return blob.sentiment.polarity  # Returns value between -1 and 1
    except:
        return 0.0

def detect_crisis(text):
    """Detect if message contains crisis indicators"""
    text_lower = text.lower()
    return any(keyword in text_lower for keyword in CRISIS_KEYWORDS)

def get_ai_response(message, user_context=None):
    """Get response from OpenAI GPT"""
    try:
        # System prompt for mental health counseling
        system_prompt = """You are a compassionate AI mental health counselor. Your role is to:
        1. Provide supportive, empathetic responses
        2. Use active listening techniques
        3. Offer coping strategies and wellness tips
        4. Encourage professional help when needed
        5. NEVER provide medical diagnoses or replace professional therapy
        6. If you detect crisis language, immediately encourage contacting emergency services
        
        Keep responses warm, supportive, and under 200 words."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": message}
        ]
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=messages,
            max_tokens=200,
            temperature=0.7
        )
        
        return response.choices[0].message.content.strip()
    except Exception as e:
        return "I'm sorry, I'm having trouble connecting right now. Please try again in a moment, or consider reaching out to a human counselor if you need immediate support."

@chat_bp.route('/')
@login_required
def chat_home():
    # Get user's recent chat sessions
    recent_sessions = ChatSession.query.filter_by(user_id=current_user.id)\
                                     .order_by(ChatSession.session_start.desc())\
                                     .limit(5).all()
    return render_template('chat/chat.html', recent_sessions=recent_sessions)

@chat_bp.route('/new_session', methods=['POST'])
@login_required
def new_session():
    """Start a new chat session"""
    mood_before = request.json.get('mood_before')
    
    session = ChatSession(
        user_id=current_user.id,
        mood_before=mood_before
    )
    db.session.add(session)
    db.session.commit()
    
    return jsonify({'session_id': session.id, 'status': 'success'})

@chat_bp.route('/send_message', methods=['POST'])
@login_required
def send_message():
    """Send a message and get AI response"""
    data = request.json
    session_id = data.get('session_id')
    message_content = data.get('message')
    
    if not session_id or not message_content:
        return jsonify({'error': 'Missing session_id or message'}), 400
    
    # Verify session belongs to current user
    session = ChatSession.query.filter_by(id=session_id, user_id=current_user.id).first()
    if not session:
        return jsonify({'error': 'Invalid session'}), 404
    
    # Analyze sentiment and detect crisis
    sentiment = analyze_sentiment(message_content)
    crisis_detected = detect_crisis(message_content)
    
    # Save user message
    user_message = ChatMessage(
        session_id=session_id,
        message_type='user',
        content=message_content,
        sentiment_score=sentiment
    )
    db.session.add(user_message)
    
    # Update session if crisis detected
    if crisis_detected:
        session.crisis_detected = True
    
    # Get AI response
    ai_response = get_ai_response(message_content)
    
    # If crisis detected, add emergency resources to response
    if crisis_detected:
        ai_response += "\n\n🚨 I'm concerned about you. Please consider:\n• Emergency: 911\n• Crisis Text Line: Text HOME to 741741\n• National Suicide Prevention Lifeline: 988"
    
    # Save AI response
    bot_message = ChatMessage(
        session_id=session_id,
        message_type='bot',
        content=ai_response
    )
    db.session.add(bot_message)
    db.session.commit()
    
    return jsonify({
        'response': ai_response,
        'crisis_detected': crisis_detected,
        'sentiment': sentiment
    })

@chat_bp.route('/end_session', methods=['POST'])
@login_required
def end_session():
    """End a chat session"""
    data = request.json
    session_id = data.get('session_id')
    mood_after = data.get('mood_after')
    
    session = ChatSession.query.filter_by(id=session_id, user_id=current_user.id).first()
    if not session:
        return jsonify({'error': 'Invalid session'}), 404
    
    session.session_end = datetime.utcnow()
    session.mood_after = mood_after
    
    # Calculate average sentiment for the session
    messages = ChatMessage.query.filter_by(session_id=session_id, message_type='user').all()
    if messages:
        avg_sentiment = sum(msg.sentiment_score or 0 for msg in messages) / len(messages)
        session.sentiment_score = avg_sentiment
    
    db.session.commit()
    
    # Award points for completing a session
    current_user.points += 10
    db.session.commit()
    
    return jsonify({'status': 'success', 'points_earned': 10})

@chat_bp.route('/history')
@login_required
def chat_history():
    """View chat history"""
    sessions = ChatSession.query.filter_by(user_id=current_user.id)\
                               .order_by(ChatSession.session_start.desc()).all()
    return render_template('chat/history.html', sessions=sessions)

@chat_bp.route('/session/<int:session_id>')
@login_required
def view_session(session_id):
    """View a specific chat session"""
    session = ChatSession.query.filter_by(id=session_id, user_id=current_user.id).first_or_404()
    messages = ChatMessage.query.filter_by(session_id=session_id)\
                                .order_by(ChatMessage.timestamp).all()
    return render_template('chat/session.html', session=session, messages=messages)
